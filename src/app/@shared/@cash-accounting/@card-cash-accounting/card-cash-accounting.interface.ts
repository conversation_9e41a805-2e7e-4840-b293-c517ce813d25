export interface TerminalInfo {
    id?: number;
    tname?: string;
    city?: number;
    place?: number;
    paid_status?: number;
    cash_turnover_info?: CashTurnoverInfo[];
    quick_sale_info?: any[];
    isCollapsed?: boolean;
}

export interface CashTurnoverInfo {
    id?: number;
    operation_type?: string;
    operation_id?: number;
    place_id?: number;
    shift_id?: number;
    user_id?: number;
    created_at?: Date;
    updated_at?: Date;
    date?: Date;
    payment_method?: PaymentMethod;
    terminal_id?: number;
    turn_over_info?: TurnOverInfo | null;
    wage_info?: any[];
    invoice_history?: null;
    debt_info?: null;
    creator?: Creator;
    collection_info?: CollectionInfo | null;
}

export interface CollectionInfo {
    id?: number;
    journal_id?: number;
    operation_id?: number;
    place_id?: number;
    user_id?: number;
    state?: string;
    type?: string;
    date?: null;
    created_at?: Date;
    updated_at?: Date;
    user_info?: Creator;
    transaction_info?: TransactionInfo;
    cash_history?: CashHistory[];
}

export interface CashHistory {
    id?: number;
    cash_bills?: CashBill[];
    cash?: number;
    collection_id?: number;
    created_at?: Date;
    updated_at?: Date;
    state?: string;
    payment_method?: PaymentMethod;
    user_id?: null;
    terminal_id?: null;
    terminal_info?: null;
}

export interface CashBill {
    id?: number;
    collection_cash_id?: number;
    bill_id?: number;
    count?: number;
    bill_rate?: number;
    cash?: number;
    sum?: number;
    payment_method?: PaymentMethod;
    state?: State;
    created_at?: Date;
    updated_at?: Date;
}

export enum PaymentMethod {
    Cash = "cash",
    Card = "card",
}

export enum State {
    Pending = "pending",
}

export interface TransactionInfo {
    id?: number;
    name?: string;
    url?: string;
    operation_id?: number;
    type?: string;
    created_at?: null;
    updated_at?: null;
}

export interface Creator {
    name?: string;
    surname?: string;
    id?: number;
}

export interface TurnOverInfo {
    id?: number;
    cash?: number;
    type?: string;
    user_id?: number;
    payment_method?: PaymentMethod;
    operation_id?: number;
    place_id?: number;
    comment?: string;
    state?: string;
    shift_id?: null;
    created_at?: Date;
    updated_at?: Date;
    date?: Date;
    journal_id?: number;
    terminal_id?: null;
    user_info?: Creator;
    transaction_info?: TransactionInfo;
    terminal_info?: null;
}
