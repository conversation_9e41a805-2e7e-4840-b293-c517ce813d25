import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';

import {TerminalInfo} from './card-cash-accounting.interface';
import {DateService} from '../../@date/date.service';
import {AppApiUrls} from '../../../app.api.urls';
import {map} from "rxjs/operators";
import {OperationType} from "../../../cash-accounting/chief-cook-cash-accounting/interface";

@Injectable()
export class CardCashAccountingService {
    constructor(private http: HttpClient,
                private date: DateService) {
    }

    get(): Observable<TerminalInfo[]> {
        return this.http.get<TerminalInfo[]>(AppApiUrls.terminalCash(), {
            params: {
                from: this.date.getFormatDate(),
                to: this.date.getFormatDate(),
                place_id: localStorage.getItem('place')
            }
        }).pipe(map(element => {
            element.map(el => {
                el.cash_turnover_info.map(item => {

                    if (item.operation_type === OperationType.Collection) {
                        item.collection_info.cash_history.map(cashHistory => {
                            cashHistory.cash = cashHistory.cash_bills.reduce((a, b) => {
                                return a + b.bill_rate * b.count;
                            }, 0);
                            return cashHistory;
                        });
                    }
                    return item;
                });
                return el;
            });
            return element;
        }));
    }
}
