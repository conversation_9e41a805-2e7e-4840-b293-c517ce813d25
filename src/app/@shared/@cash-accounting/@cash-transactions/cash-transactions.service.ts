import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {Observable} from 'rxjs';

import {AppApiUrls} from '../../../app.api.urls';
import {CashTransaction} from "./cash-transaction.interface";

@Injectable()
export class CashTransactionsService {
    constructor(private http: HttpClient) {
    }

    update(transaction: CashTransaction): Observable<CashTransaction> {
        return this.http.put<CashTransaction>(AppApiUrls.cashTransactions() + '/' + transaction.id, transaction);
    }

    get(): Observable<CashTransaction[]> {
        return this.http.get<CashTransaction[]>(AppApiUrls.cashTransactions());
    }

    create(transaction: CashTransaction): Observable<CashTransaction> {
        return this.http.post<CashTransaction>(AppApiUrls.cashTransactions(), transaction);
    }

    delete(transaction: CashTransaction): Observable<CashTransaction> {
        return this.http.delete<CashTransaction>(AppApiUrls.cashTransactions() + '/' + transaction.id);
    }
}
