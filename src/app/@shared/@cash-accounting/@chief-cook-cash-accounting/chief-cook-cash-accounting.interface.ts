import {Observable} from 'rxjs';
import {RootObject} from '../../../cash-accounting/chief-cook-cash-accounting/interface';
import {Collections} from '../@client-cash-accounting/client-cash-accounting.interface';

export interface ChiefCookCashAccountingInterface {
    getOperation(operationId: number): Observable<RootObject[]>;
    createCashTurnover(json: any): Observable<any>;
    updateList(date: string): Observable<RootObject[]>;
    updateListCollections(date: string): Observable<Collections[]>;
    getOperationById(): Observable<RootObject[]>;
}
