import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {AppApiUrls} from '../../../app.api.urls';
import {BehaviorSubject, Observable} from 'rxjs';
import {map, tap} from 'rxjs/operators';
import {OperationType, RootObject} from '../../../cash-accounting/chief-cook-cash-accounting/interface';
import {Collections} from '../@client-cash-accounting/client-cash-accounting.interface';
import {ChiefCookCashAccountingInterface} from './chief-cook-cash-accounting.interface';

@Injectable()

export class ChiefCookCashAccountingService implements ChiefCookCashAccountingInterface {
    allOperation$: Observable<any[]> = this.http.get<any[]>(AppApiUrls.getAllTransactions());
    allData$: BehaviorSubject<RootObject[]> = new BehaviorSubject<RootObject[]>([]);
    dataForReport: RootObject[];
    operationId: number;
    allCollections$: Observable<Collections>;
    public AllCostType: any;
    public operation = '1';
    public comment = ' ';

    constructor(private http: HttpClient) {

    }

    createCashTurnover(json: any): Observable<any> {
        return this.http.post(AppApiUrls.cashTurnover(), json);
    }

    getOperation(operationId: number): Observable<RootObject[]> {
        return this.allOperation$.pipe(
            map(results => results.filter(t => t.id == operationId))) as Observable<RootObject[]>;
    }

    getOperationById(): Observable<RootObject[]> {
        return this.allData$.pipe(
            map(json => json.filter(t => t.id == this.operationId))
        );
    }

    updateList(date: string, shiftId?: number): Observable<RootObject[]> {
        return this.get(date, shiftId).pipe(tap(value => {
            this.allData$.next(value);
        }));
    }

    get(date: string, shiftId?: number): Observable<RootObject[]> {
        const params = (shiftId) ? {
            place_id: localStorage.getItem('place'),
            date,
            shift_id: shiftId
        } : {
            place_id: localStorage.getItem('place'),
            date
        };
        return this.http.get<RootObject[]>(AppApiUrls.cashTurnover(), {
            params: params as any
        }).pipe(map(element => {
            element.map(item => {
                if (item.operation_type === OperationType.Collection) {
                    item.collection_info.cash_history.map(cashHistory => {
                        cashHistory.cash = cashHistory.cash_bills.reduce((a, b) => {
                            if (b.bill_info.bill_type == 'card') {
                                cashHistory.comment = 'Інкасація карток';
                            }else if(b.bill_info.bill_type == 'cash'){
                                cashHistory.comment = 'Інкасація грошей';
                            }
                            return a + b.bill_rate * b.count;
                        }, 0);
                        return cashHistory;
                    });
                }
                return item;
            });
            return element;
        }));
    }

    getSum(): Observable<any> {
        return this.allData$.pipe(
            map(objects => {
                objects.map(object => {
                    object.infoSum = {
                        sumCost: 0,
                        sumArrival: 0,
                    };
                    if (object.operation_type == 'turnover' && object.turn_over_info.type == 'arrival') {
                        object.infoSum.sumArrival = object.turn_over_info.cash;
                    } else if (object.operation_type == 'collection') {
                        object.infoSum.sumArrival = object.collection_info.cash_history.reduce((a, b) => {
                            let sum = 0;
                            if (b.state == 'done') {
                                sum = b.cash;
                            }
                            return a + sum;
                        }, 0);
                    } else if ((object.operation_type == 'debt' && object.debt_info.company_debt)) {
                        object.infoSum.sumArrival = object.debt_info.company_debt;
                    }

                    if (object.operation_type && object.operation_type == 'invoice') {
                        object.infoSum.sumCost = object.invoice_history.sum;
                    } else if (object.operation_type == 'wage') {
                        object.infoSum.sumCost = object.wage_info.reduce((a, b) => {
                            let sum = 0;
                            if (b.state === 'done') sum = b.wage;
                            return a + sum;
                        }, 0);
                    } else if ((object.operation_type == 'turnover' && object.turn_over_info.type == 'cost')) {
                        object.infoSum.sumCost = object.turn_over_info.cash;
                    } else if ((object.operation_type == 'debt' && object.debt_info.user_debt)) {
                        object.infoSum.sumCost = object.debt_info.user_debt;
                    }
                    return object;
                });
                return {
                    sumCost: objects.filter(el => el.payment_method == 'cash').reduce((a, b) => a + b.infoSum.sumCost, 0),
                    sumArrival: objects.filter(el => el.payment_method == 'cash').reduce((a, b) => a + b.infoSum.sumArrival, 0),
                    sumCashlessCost: objects.filter(el => el.payment_method == 'card').reduce((a, b) => a + b.infoSum.sumCost, 0),
                    sumCashlessArrival: objects.filter(el => el.payment_method == 'card').reduce((a, b) => a + b.infoSum.sumArrival, 0),
                    operation_type: 'sumInfo'
                } as any;
            }));
    }

    updateListCollections(date: string): Observable<Collections[]> {
        return this.http.get<Collections[]>(AppApiUrls.StartShiftForCollection(), {
            params: {
                place: localStorage.getItem('place'),
                date
            }
        });
    }

    GetAllCostType = (content, type) => {
        const body = new HttpParams()
            .set('type', type);
        return this.http.post<any>(AppApiUrls.GetCostType,
            body);
    }

    findForReport = (id, subId) => {
        this.allData$.pipe(map(json =>
            json.filter(t => t.id == id)
        )).subscribe(json => {
            this.dataForReport = json;
            if (subId) {
                this.dataForReport[0].collection_info.firstCashHistory = this.dataForReport[0].collection_info.cash_history.find(t => t.id == subId);
            }
        });
    }
    getShiftList = (): Observable<any> => {
        return this.http.get(AppApiUrls.shiftList(), {
            params: {
                place_id: localStorage.getItem('place')
            }
        });
    }
    getShiftInfo = (): Observable<any> => {
        return this.http.get(AppApiUrls.shiftInfo(), {
            params: {
                place_id: localStorage.getItem('place'),
                user_id: localStorage.getItem('id')
            }
        });
    }

    updateByJournalId(id: number, paymentMethod: string, operationType: string, terminalId: number): Observable<void> {
        const data = {
            payment_method: paymentMethod,
            operation_type: operationType,
            terminal_id: terminalId
        };
        return this.http.put<void>(AppApiUrls.turnoverJournal + id, data);
    }

}
