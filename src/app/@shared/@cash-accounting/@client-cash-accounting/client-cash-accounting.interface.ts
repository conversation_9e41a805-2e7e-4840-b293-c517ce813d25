import {Observable} from 'rxjs';
import {Collection} from '../../@collection/collection.interface';

export interface Bills {
    id: string;
    bill_id?: string;
    bill_rate: number;
    bills: number;
    bill_type: string;
    count: number;
    sum: number;
}

export interface Collections {
    id: number;
    journal_id: number;
    operation_id: number;
    fullname?: string;
    place_id: number;
    user_id: number;
    state: string;
    type: string;
    date: null;
    created_at: Date;
    updated_at: Date;
    user_info: UserInfo;
    transaction_info: TransactionInfo;
    cash_history: CashHistory[];
}

export interface CashHistory {
    id: number;
    collection_id: number;
    cash: number;
    state?: string;
    created_at: Date;
    updated_at: Date;
}

export interface TransactionInfo {
    id: number;
    name: string;
    url: string;
    operation_id: number;
    type: string;
}

export interface UserInfo {
    name: string;
    surname: string;
    id: number;
}

export interface ClientCashAccountingInterface {
    getBills(): Observable<Bills[]>;

    createCollection(colId: number, billType: string): Observable<any>;

    updateCollection(collection: Collection): Observable<any>;

    startShift(mode: string): Observable<any>;

    getCollections(): Observable<Collections[]>;

    getCashInfo(mode: string): Observable<CashInfo | CashInfoRL>;
}

export interface CashInfo {
    card: number;
    cash: number;
    card_paid: number;
    cash_paid: number;
    collection_done: number;
}

export interface CashInfoRL {
    orders_card?: number;
    orders_cash?: number;
    orders_card_paid?: number;
    orders_card_unpaid?: number;
    orders_cash_paid?: number;
    orders_cash_unpaid?: number;
    delivery?: number;
    orders_cash_debt?: number;
    delivery_paid?: number;
    collection_done?: number;
}
