import {Observable} from 'rxjs';
import {paymentMethod} from "../../../cash-accounting/chief-cook-cash-accounting/interface";

export interface SalaryInterface {
    usersReceivedWages(): Observable<UsersReceivedWages[]>;
    createWageForUsers(array: CreatedPayForUsers): Observable<UsersReceivedWages>;
}

export interface UsersReceivedWages {
    name: string;
    surname: string;
    total_wage: number;
    payment_method?: paymentMethod;
    total_fine: number;
    total_prize: number;
}

export interface CreatedPayForUsers {
    user_id: string;
    id?: number;
    place_id: string;
    operation_id: number;
    payment_method?: paymentMethod;
    date: Date;
    wages: Wage[];
}

export interface Wage {
    operation_id: number;
    place_id: string;
    user_id: number;
    date: Date;
    payer_id: string;
    payment_method?: paymentMethod;
    wage: number;
    fine: number;
    prize: number;
    hours: number;
}

