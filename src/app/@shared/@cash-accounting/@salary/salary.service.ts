import {Injectable} from '@angular/core';
import {HttpClient} from '@angular/common/http';
import {AppApiUrls} from '../../../app.api.urls';
import {DateService} from '../../@date/date.service';
import {Observable} from 'rxjs';
import {CreatedPayForUsers, SalaryInterface, UsersReceivedWages} from './salary.interface';

@Injectable()

export class SalaryService implements SalaryInterface {
    constructor(private http: HttpClient, private date: DateService) {
    }

    createWageForUsers(array: CreatedPayForUsers): Observable<UsersReceivedWages> {
        return this.http.post<UsersReceivedWages>(AppApiUrls.WagesCA(), array);
    }
    updateWageForUsers(json: CreatedPayForUsers): Observable<UsersReceivedWages> {
        return this.http.put<UsersReceivedWages>(AppApiUrls.WagesCA() + '/' + json.id, json);
    }
    usersReceivedWages(): Observable<UsersReceivedWages[]> {
        return this.http.get<UsersReceivedWages[]>(AppApiUrls.doneWages(), {
            params: {
                date: this.date.getFormatDate()
            }
        });
    }
}
