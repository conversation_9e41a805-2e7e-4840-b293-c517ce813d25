import {Cities, Place} from "../../../system/interface";

export interface TransfersCash {
    data: TransfersCashInfo | TransfersCashInfo[];
    place_info: Place;
    city_id: Cities;
}

export interface TransfersCashInfo {
    id?: number;
    current_collection_id?: number;
    target_collection_id?: number;
    payment_method?: string;
    state?: string;
    created_at?: Date;
    updated_at?: Date;
    current_collection_info?: TCollectionInfo;
    target_collection_info?: TCollectionInfo;
    transfer_bills?: TransferBill[];
}

export interface TCollectionInfo {
    id?: number;
    journal_id?: number;
    operation_id?: number;
    place_id?: number;
    user_id?: number;
    state?: string;
    type?: string;
    date?: null;
    created_at?: Date;
    updated_at?: Date;
    user_info?: UserInfo;
}

export interface UserInfo {
    name?: string;
    surname?: string;
    id?: number;
}

export interface TransferBill {
    id?: number;
    transfer_id?: number;
    bill_id?: number;
    count?: number;
    bill_rate?: number;
    sum?: null;
    payment_method?: string;
    state?: string;
    created_at?: Date;
    updated_at?: Date;
}
