import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';

import {TransfersCash} from './transfers-cash.interface';
import {DateService} from '../../@date/date.service';
import {AppApiUrls} from '../../../app.api.urls';

@Injectable()
export class TransfersCashService{
    constructor(private http: HttpClient, private date: DateService) {
    }
    get(): Observable<TransfersCash[]>{
        return this.http.get<TransfersCash[]>(AppApiUrls.cashTransfer(), {
            params: {
                from: this.date.getFormatDate(),
                to: this.date.getFormatDate(),
                place_id: localStorage.getItem('place'),
                city_id: localStorage.getItem('city')
            }
        });
    }
}
