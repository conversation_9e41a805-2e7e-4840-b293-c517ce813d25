import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {AppApiUrls} from '../../../app.api.urls';
import {NgbAccordion, NgbDateStruct} from '@ng-bootstrap/ng-bootstrap';
import {AppService} from '../../app.service';
import {Alltoasts} from '../../../toasts/alltoasts';

@Injectable({providedIn: 'root'})

export class AccountantCashAccountingService{

 allTerminals: any;
  allOrdersTerminal: any;

 constructor(private http: HttpClient, private appService: AppService, private Alltoasts: Alltoasts) {

 }
  // @ts-ignore
  model: NgbDateStruct = this.appService.GetDate()
    .subscribe(result => {
      // @ts-ignore
      this.model = result;
    });



  // fetchAll = () => {
  //   const body = new HttpParams()
  //     .set('place', localStorage.getItem('place'))
  //     .set('date', (`${this.model.year}-${this.model.month}-${this.model.day}`) as string);
  //   return this.http.post<any>(AppApiUrls.GetTerminalsSum,
  //     body)
  //     .subscribe(
  //       (val) => {
  //         this.allTerminals = val;
  //       },
  //       response => {
  //         console.log('POST call in error', response);
  //         this.Alltoasts.showError();
  //       },
  //       () => {
  //
  //       });
  // }

  // getOrdersTerminal = id => {
  //   const body = new HttpParams()
  //     .set('place', localStorage.getItem('place'))
  //     .set('date', (`${this.model.year}-${this.model.month}-${this.model.day}`) as string)
  //     .set('id', id);
  //   return this.http.post<any>(AppApiUrls.GetTerminalOrders,
  //     body)
  //     .subscribe(
  //       (val) => {
  //         this.allOrdersTerminal = val;
  //       },
  //       response => {
  //         console.log('POST call in error', response);
  //       },
  //       () => {
  //       });
  // }


}
