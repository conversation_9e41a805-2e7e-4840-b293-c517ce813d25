import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AppApiUrls} from '../../app.api.urls';
import {CitiesInterface} from './cities.interface';
import {HttpClient} from '@angular/common/http';
import {Cities, Place, SettingsData, SettingsDataOption} from 'src/app/system/interface';
import {PhoneOperatorData} from '../../phone-operator/interface/operator.interface';

@Injectable({providedIn: 'root'})
export class CitiesService implements CitiesInterface {
    constructor(private http: HttpClient) { }

    getCities(): Observable<Cities[]> {
        return this.http.get<Cities[]>(AppApiUrls.GetCities());
    }

    getCity(id: number): Observable<Cities> {
        return this.http.get<Cities>(AppApiUrls.GetCities(id));
    }

    getPlace(id: number): Observable<Place> {
        return this.http.get<Place>(AppApiUrls.GetPlaces(id));
    }

    getPlaces(): Observable<Place[]> {
        return this.http.get<Place[]>(AppApiUrls.GetPlaces());
    }

    updateCity( id: number, city: Cities): Observable<Cities> {
        return this.http.put<Cities>(AppApiUrls.GetCities() + id, city);
    }

    updatePlace(place: Place): Observable<Place> {
        return this.http.put<Place>(AppApiUrls.GetPlaces(place.id), place);
    }

    postPlace(place: Place): Observable<Place> {
        return this.http.post<Place>(AppApiUrls.GetPlaces(), place);
    }

    getPhoneOperator(): Observable<PhoneOperatorData> {
        return this.http.get<PhoneOperatorData>(AppApiUrls.phoneOperator());
    }

    getSettings(cityId: number): Observable<any> {
        return this.http.get<any>(AppApiUrls.citySettings(), {
            params: {
                city_id: cityId
            }
        });
    }

    getSettingsByID(id: number): Observable<SettingsDataOption> {
        return this.http.get<SettingsDataOption>(AppApiUrls.citySettings() + '/' + id );
    }

    postSettings(cityID?: number, type?: string): Observable<SettingsDataOption> {
        return this.http.post<SettingsDataOption>(AppApiUrls.citySettings(), {}, {
            params: {
                city_id: cityID,
                type
            }
        } );
    }

    updateSettings(id: number, body: Partial<SettingsData>): Observable<SettingsDataOption> {
        return this.http.put<SettingsDataOption>(AppApiUrls.citySettings() + '/' + id, body);
    }
}
