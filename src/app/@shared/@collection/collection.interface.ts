import {Observable} from 'rxjs';

export interface CollectionInterface {

    createNewCollection(ingredients: Ingredient[], date: string): Observable<any>;

    getDatesCollection(): Observable<DatesCollection[]>;

    getInfoAboutCollection(id: number): Observable<Collection>;

    updateCollection(ingredients: Collection): Observable<any>;

    delete(id: number): Observable<Collection>;
}

export interface Collection {
    id: number;
    place_id: string;
    created_at: Date;
    status?: string;
    ingredients: Ingredient[];
}

export interface Ingredient {
    name: string;
    id: number;
    ing_id?: number;
    type: string;
    status?: string;
    quantity?: number;
}

export interface DatesCollection {
    date_time: Date;
    id: number;
}
