import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Collection, CollectionInterface, DatesCollection, Ingredient} from './collection.interface';
import {Observable} from 'rxjs';
import {AppApiUrls} from '../../app.api.urls';

@Injectable({providedIn: 'root'})

export class CollectionService implements CollectionInterface{
    constructor(private http: HttpClient) {
    }

    delete(id: number): Observable<Collection> {
        return this.http.delete<Collection>(AppApiUrls.deleteCollection() + id);
    }

    createNewCollection(ingredients: Ingredient[], date: string): Observable<any> {
            const data = {
                place_id: localStorage.getItem('place'),
                date_time: `${date} 23:59:59`,
                ingredients
            };
            const body = new HttpParams()
                .set('data', JSON.stringify(data));
            return this.http.post<any>(AppApiUrls.CreateCollection,
                data);
    }

    getDatesCollection(): Observable<DatesCollection[]> {
            const data = {
                place_id: localStorage.getItem('place')
            };
            const body = new HttpParams()
                .set('data', JSON.stringify(data));
            return this.http.post<DatesCollection[]>(AppApiUrls.GetDatesCollection,
                body,
                {
                    params: {
                        place_id: localStorage.getItem('place')
                    }
                }
            );
    }

    getInfoAboutCollection(id: number): Observable<Collection> {
            const body = new HttpParams()
                .set('data', JSON.stringify({id}));
            return this.http.post<Collection>(AppApiUrls.GetInfoAboutCollection,
                body, {
                params: {
                    id
                }
                });
    }
    updateCollection(ingredients: Collection): Observable<any> {
            const body = new HttpParams()
                .set('data', JSON.stringify(ingredients));
            return this.http.post<any>(AppApiUrls.CollectionUpdate,
                ingredients);
    }

}
