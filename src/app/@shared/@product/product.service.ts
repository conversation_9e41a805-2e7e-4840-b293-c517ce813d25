import {Injectable} from '@angular/core';
import {Observable} from 'rxjs';
import {AppApiUrls} from '../../app.api.urls';
import {HttpClient} from '@angular/common/http';
import {Product} from "../../recommended-products/interface/recommended-products.interface";

@Injectable({providedIn: 'root'})
export class ProductService {
    constructor(private http: HttpClient) {
    }

    getProducts(): Observable<Product[]> {
        return this.http.get<Product[]>(AppApiUrls.products());
    }
}
