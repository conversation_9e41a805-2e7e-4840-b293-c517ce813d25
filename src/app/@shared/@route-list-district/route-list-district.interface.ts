import {Observable} from 'rxjs';
import {Place} from '../../system/interface';
import {Users} from '../@users/interfaces/users.interface';
import {Author, District, Orders} from '../../quick-sale/all-quick-sale/interface';

export interface RouteListDistrictInterface {
    createRoute(route: { place_id: number; manager_id: number; courier_id: number; orders: [{ id: number }] }): Observable<DistrictRouteList>;

    getRoutes(from: string, to: string): Observable<DistrictRouteList[]>;

    getRoute(id: number): Observable<DistrictRouteList>;

    getOrderByOrderNumber(orderNumber: number, date: string): Observable<OrderByOrderNumber>;
}

export interface DistrictRouteList {
    id: number;
    place_id: number;
    manager_id: number;
    courier_id: number;
    state: string;
    pay_state: string;
    created_at: Date;
    updated_at: Date;
    place: Place;
    manager: Users;
    courier: Users;
    info: Info[];
}

export interface Info {
    id: number;
    order_id: number;
    route_id: number;
    created_at: Date;
    updated_at: Date;
    order: OrdersRoute;
}

export interface OrderByOrderNumber{
    address: string;
    id?: number;
    comment: string;
    order_id: number;
    order_number: number;
    sum: number;
    payment_type: string;
    phone: string;
    created_at?: string;
}

export interface OrderByOrderNumberEdit{
    id: number;
    info: OrderByOrderNumber[];
    courier: CourierRoute;
}

export interface CourierRoute {
    id: number;
    name: string;
    surname: string;
}

export interface OrdersRoute{
    id: number;
    order_number: number;
    phone: string;
    district_id: number;
    zone_id: number;
    street: string;
    house?: string;
    attic: string;
    apt: string;
    floor: string;
    stick: string;
    stick_edu: string;
    terminal: number;
    terminalcheck: any;
    sale: number;
    pickupcheck: any;
    birthdaycheck: any;
    futurecheck: any;
    futuredate: string;
    futuretime: string;
    comment: string;
    author_id: string;
    total: number;
    totalwithoutsale: number;
    place: number;
    order: any;
    status: any;
    pickup_pay: number;
    district_price: number;
    place_name: string;
    author_name?: string;
    author_surname?: string;
    district?: District;
    author?: Author;
    cashierInfo: Author;
    duplicate?: boolean;
}
