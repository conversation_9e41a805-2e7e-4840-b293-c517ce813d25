import {Pipe, PipeTransform} from "@angular/core";

@Pipe({
    name: 'ReducePipe'
})
export class RouteListDistrictReducePipe implements PipeTransform {
    transform(value: any, type: string): any {
        switch (type) {
            case 'delivery':
                return value.reduce((a, b) => a + b.order.district.price, 0).toFixed(2);
            case 'cash':
                return value.reduce((a, b) => {
                    let sum = 0;
                    if (b.order.terminalcheck != 1) {
                        sum = b.order.total;
                    }
                    return a + sum;
                }, 0).toFixed(2);
            case 'cashless':
                return value.reduce((a, b) => {
                    let sum = 0;
                    if (b.order.terminalcheck == 1) {
                        sum = b.order.total;
                    }
                    return a + sum;
                }, 0).toFixed(2);
        }
    }
}
