import {Injectable} from '@angular/core';
import {DistrictRouteList, OrderByOrderNumber, OrderByOrderNumberEdit, RouteListDistrictInterface} from './route-list-district.interface';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {AppApiUrls} from '../../app.api.urls';
import {Users} from '../@users/interfaces/users.interface';
import {map} from 'rxjs/operators';

@Injectable()
export class RouteListDistrictService implements RouteListDistrictInterface {
    constructor(private http: HttpClient) {
    }

    users$: Observable<Users[]> = this.http.get<Users[]>(AppApiUrls.getAllUsers(),
        {
            params: {
                place_id: localStorage.getItem('place'),
                prof_id: 7
            }
        }).pipe(map(element => element.map(user => {
        user.fullname = `${user.name} ${user.surname}`;
        return user;
    })));

    createRoute(route: { place_id: number; manager_id: number; orders: [{ id: number }];
        courier_id: number }): Observable<DistrictRouteList> {
        return this.http.post<DistrictRouteList>(AppApiUrls.districtRouteList(), route);
    }

    getRoutes(from: string, to: string): Observable<DistrictRouteList[]> {
        return this.http.get<DistrictRouteList[]>(AppApiUrls.districtRouteList(), {
            params: {
                place_id: localStorage.getItem('place'),
                from,
                to
            }
        });
    }

    getRoute(id: number): Observable<DistrictRouteList> {
        return this.http.get<DistrictRouteList>(AppApiUrls.districtRouteList() + id);
    }

    getOrderByOrderNumber(orderNumber: number, date: string): Observable<OrderByOrderNumber> {
        return this.http.get<OrderByOrderNumber>(AppApiUrls.getOrderForEditInRouteList, {
            params: {
                date,
                city_id: localStorage.getItem('city'),
                order_number: orderNumber as any
            }
        });
    }

    getInfoByRouteId(id): Observable<DistrictRouteList> {
        return this.http.get<DistrictRouteList>(AppApiUrls.districtRouteList(), {
            params: {
                route_list_id: id
            }
        });
    }

    getInfoByRouteIdEdit(id): Observable<DistrictRouteList> {
        return this.http.get<DistrictRouteList>(AppApiUrls.districtRouteList() + id);
    }

    editRoute(id: number, body): Observable<OrderByOrderNumberEdit> {
        return this.http.put<OrderByOrderNumberEdit>(AppApiUrls.districtRouteList() + id, body);
    }

    getRouteEdit(id: number): Observable<OrderByOrderNumberEdit> {
        return this.http.get<OrderByOrderNumberEdit>(AppApiUrls.districtRouteList(), {
            params: {
                route_list_id: id
            }
        });
    }

}
