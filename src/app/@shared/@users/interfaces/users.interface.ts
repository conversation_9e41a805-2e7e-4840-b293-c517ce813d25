import {Observable} from 'rxjs';
import {paymentMethod} from '../../../cash-accounting/chief-cook-cash-accounting/interface';
import {ProfInfo} from '../../interfaces';

export interface UsersInterface {
    users$: Observable<Users[]>;
    professions$: Observable<Profession[]>;
    userLeave(id: number): Observable<Users[]>;
    userRegistration(userInfo): Observable<Users[]>;
    getUser(): Observable<Users[]>;
    userDataReset(id: number): Observable<Users>;
}
export interface Profession {
    id: number;
    name: string;
    cash: number;
}

export type Professions = Profession[];

export interface Users {
    id: number;
    fullname?: string;
    payment_method?: paymentMethod;
    name: string;
    surname: string;
    telephone: string;
    password: null | string;
    address: string;
    description: any;
    date_added: Date;
    image: string;
    token: null | string;
    platform: Platform | null;
    lang: Lang;
    courier_id: string;
    price_single: number;
    salary: number;
    whitelist: number;
    hours?: number;
    fine?: number;
    prize?: number;
    professionId?: number;
    user_settings: UserSettings;
    user_profs: UserProfs[];
    city_id?: string;
    check_type?: string;
    tg_id?: number;
}

export interface UserProfs {
    id?: number;
    user_id?: number;
    prof_id: number;
    profs?: string;
    price_single: number;
    salary: number;
    prof_info?: ProfInfo;
}

export enum Lang {
    Ru = 'ru',
    Ua = 'ua',
}

export enum Platform {
    And = 'and',
    Ios = 'ios',
}

export interface UserSettings {
    user_profs: UserProfs;
}

