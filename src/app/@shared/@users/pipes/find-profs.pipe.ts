import {Pipe, PipeTransform} from '@angular/core';
import { Users} from '../interfaces/users.interface';

@Pipe({
    name: 'findProfs'
})
export class FindProfsPipe implements PipeTransform{
    transform(professions: Users[], profId: any): Users[] {
        if (profId == 'null' || !profId) {
            return professions;
        }
        return professions.filter(user => user.professionId == profId);
    }
}
