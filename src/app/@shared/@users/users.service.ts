import { Injectable } from '@angular/core';
import {Observable} from 'rxjs';
import {AppApiUrls} from '../../app.api.urls';
import {Professions, Users, UsersInterface} from './interfaces/users.interface';
import {HttpClient} from '@angular/common/http';
import {map, tap} from 'rxjs/operators';
import {UnitData} from '../../_product-management/interfaces/techcards-product.interface';

@Injectable({
  providedIn: 'root'
})
export class UsersService implements UsersInterface{

    placeId =  localStorage.getItem('place');
    constructor(private http: HttpClient) { }

    users$: Observable<Users[]> = this.http.get<Users[]>(AppApiUrls.getAllUsers(),
      {
        params: {
          place_id: localStorage.getItem('place'),
        }
      }).pipe(map(element => element.map(user => {
          user.fullname = `${user.name} ${user.surname}`;
          return user;
    })));

    professions$: Observable<Professions> = this.http.get<Professions>(AppApiUrls.profession());

    userLeave(id: number): Observable<Users[]> {
        const body = new FormData();
        body.append('id_user', String(id));
        return this.http.post<Users[]>(AppApiUrls.userLeaveAdd(), body);
    }

    userDataReset(id: number): Observable<Users> {
        return this.http.delete<Users>(AppApiUrls.userDataReset() + id);
    }

    userRegistration(userInfo): Observable<Users[]> {
        return this.http.post<Users[]>(AppApiUrls.getAllUsers(), userInfo);
    }

    getUser(): Observable<Users[]> {
        return this.http.get<Users[]>(AppApiUrls.getAllUsers(),
            {
                params: {
                    place_id: localStorage.getItem('place'),
                }
            }).pipe(map(element => element.map(user => {
            user.fullname = `${user.name} ${user.surname}`;
            return user;
        })));
    }

    getLeaveList(): Observable<Users[]>{
        return this.http.get<Users[]>(AppApiUrls.userLeaveList() + '?place_id=' + this.placeId);
    }

    userAdd(id: number): Observable<Users[]> {
        const body = new FormData();
        body.append('id_user', String(id));
        return this.http.post<Users[]>(AppApiUrls.userLeaveRemove(), body);
    }

    userUpdate(id: number, body): Observable<Users[]> {
        return this.http.put<Users[]>(AppApiUrls.visitUpdate()  + '/' + id, body );
    }
}
