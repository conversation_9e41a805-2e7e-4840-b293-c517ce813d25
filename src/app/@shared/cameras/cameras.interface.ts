import {Observable} from 'rxjs';
import {Cities, Place} from '../../system/interface';

export interface CamerasInterface {
    getCameras(): Observable<Camera[]>;
    createCamera(camera: Camera): Observable<Camera>;
    deleteCamera(id: number): Observable<Camera>;
    editCamera(camera: Camera, id: number): Observable<Camera>;
    getCamera(id: number): Observable<Camera>;
}

export interface Camera {
    id?: number;
    name: string;
    login: string;
    password: string;
    ip: string;
    port_panel: number;
    port_rtsp: number;
    city_id?: number;
    place_id?: number;
    place?: Place;
    city?: Cities;
    is_disabled: string;
    state?: CameraState;

}

export enum CameraState {
    offline = 'offline',
    online = 'online',
    streaming = 'streaming'
}
