import {Injectable} from '@angular/core';
import {Camera, CamerasInterface} from './cameras.interface';
import {Observable} from 'rxjs';
import {HttpClient} from '@angular/common/http';
import {AppApiUrls} from '../../app.api.urls';

@Injectable()
export class CamerasService implements CamerasInterface {

    constructor(private http: HttpClient) {
    }

    createCamera(camera: Camera): Observable<Camera> {
        return this.http.post<Camera>(AppApiUrls.cameras(), camera);
    }

    deleteCamera(id: number): Observable<Camera> {
        return this.http.delete<Camera>(AppApiUrls.cameras() + id);
    }

    editCamera(camera: Camera, id: number): Observable<Camera> {
        return this.http.put<Camera>(AppApiUrls.cameras() + id, camera);
    }

    getCameras(): Observable<Camera[]> {
        return this.http.get<Camera[]>(AppApiUrls.cameras(), {
            params: {
                city_id: localStorage.getItem('city')
            }
        });
    }

    getCamera(id: number): Observable<Camera> {
        return this.http.get<Camera>(AppApiUrls.cameras() + id);
    }
}
