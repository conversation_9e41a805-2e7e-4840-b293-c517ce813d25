import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {Alltoasts} from '../../toasts/alltoasts';
import {AppService} from '../app.service';
import {Ngb<PERSON>alendar, NgbDateParserFormatter, NgbDateStruct, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {AppApiUrls} from '../../app.api.urls';
import {arrayOrders} from "../packagepanel/packagepanel-leftblock/array.orders";
import {IngredientsData} from "../../ingredients/interface/ingredients.interface";
import {SemifinishData} from "../../semifinish/interfaces/semfinish.interface";
import {Contragents, ContragentsData, ContragentsInfo} from "../../contragents/interface/contragents.interface";
import {Observable} from "rxjs";
import {CallCenterData} from "../../report-call-center/interface/report-call-center.interface";
import {ContrAgentsData, WabillReportsData} from "../../waybills/interface/wabills.interface";

@Injectable({providedIn: 'root'})
export class CreateWaybillService {

    placeId = localStorage.getItem('place');
    constructor(private http: HttpClient,
                private Alltoasts: Alltoasts,
                private appService: AppService,
                private modal: NgbModal,
                private calendar: NgbCalendar,
                public formatter: NgbDateParserFormatter) {
    }

    // @ts-ignore
    model: NgbDateStruct = this.appService.GetDate()
        .subscribe(result => {
            // @ts-ignore
            this.model = result;
        });
    allContr: any;
    form: any;
    allProduct: any;
    cardProduct: any = [];

    getAllProductAndContr = () => {
        this.form = new FormGroup({
            waybill_name: new FormControl(null, [Validators.required]),
            contragent: new FormControl(null, [Validators.required]),
            created_at: new FormControl(null, [Validators.required]),
            time:  new FormControl(''),
        });

        const body = new HttpParams()
            .set('place', localStorage.getItem('place'))
            .set('lang', 'ua')
            .set('city', localStorage.getItem('city'));
        return this.http.get<IngredientsData>(AppApiUrls.ingredient(), {
            params: {
                city_id: localStorage.getItem('city')
            }})
            .subscribe(
                (val) => {
                    this.allProduct = val.data;
                    this.allProduct.forEach(item => {
                        item.type = 1;
                    });
                },
                response => {
                    console.log('POST call in error', response);
                    this.Alltoasts.showError();
                },
                () => {
                    const bodyContr = new HttpParams()
                        .set('city', localStorage.getItem('city'));
                    return this.http.get<arrayOrders>(AppApiUrls.GetContrForConfirmWaybills,
                        {
                            params: {
                                city: localStorage.getItem('city')
                            }
                        })
                        .subscribe(
                            (val: any) => {
                                this.allContr = val.data as any;
                            },
                            response => {
                                console.log('POST call in error', response);
                                this.Alltoasts.showError();
                            },
                            () => {
                                return this.http.get<SemifinishData>(AppApiUrls.semifinish(),
                                    {
                                        params: {
                                            city_id: localStorage.getItem('city')
                                        }
                                    })
                                    .subscribe(
                                        (val) => {
                                            const semifinishes: any = val.data;
                                            semifinishes.forEach(item => {
                                                item.type = 2;
                                            });
                                            this.allProduct = this.allProduct.concat(semifinishes);
                                        },
                                        response => {
                                            console.log('POST call in error', response);
                                            this.Alltoasts.showError();
                                        },
                                        () => {

                                        });
                            });
                });

    }
    addNewProduct = (form, id, name, type, pdv) => {
        const card = {
            ingredient_id: id,
            ing_name: name,
            type,
            quantity: form.quantity,
            sum: ((form.sum - form.sale) * pdv).toFixed(3) ,
            presum: (form.sum * pdv).toFixed(3),
            sale: (form.sale).toFixed(3),
            price: ((((form.sum  - form.sale) * pdv) / form.quantity)).toFixed(3)
        };
        this.cardProduct.push(card);
        this.modal.dismissAll();
    }
    generateJsonAndUpdateBase = () => {
        const httpOptions = {
            name: this.form.value.waybill_name,
            contragent_id: this.form.value.contragent,
            created_at: this.form.value.created_at,
            place_id: localStorage.getItem('place'),
            author_id: localStorage.getItem('id'),
            ingredients: this.cardProduct
        };
        return this.http.post<arrayOrders>(AppApiUrls.CreateWaybill,
            httpOptions)
            .subscribe(
                (val) => {
                },
                response => {
                    console.log('POST call in error', response);
                    this.Alltoasts.showError();
                },
                () => {
                    this.form.reset();
                    this.form.value.created_at = this.calendar.getToday();
                    this.cardProduct.splice(0, this.cardProduct.length);
                    this.Alltoasts.showSuccess();
                });
    }


    getAllContragents(from: string, to: string): Observable<ContrAgentsData> {
        return this.http.get<ContrAgentsData>(AppApiUrls.reportsContragents(), {
            params: {
                date_from: from,
                date_to: to,
                place_id: this.placeId
            }
        });
    }

    getContrAgentsInfoReportByID(from: string, to: string , id: number): Observable<WabillReportsData> {
        return this.http.get<WabillReportsData>(AppApiUrls.reportsContragents() + '/' + id + '/documents', {
            params: {
                date_from: from,
                date_to: to,
                place_id: this.placeId
            }
        });
    }
}
