import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { AppApiUrls } from '../../app.api.urls';
import { NewsData, NewsDataInfo } from "../../notification/interface/notification.interface";
import { Alltoasts } from '../../toasts/alltoasts';


@Injectable({ providedIn: 'root' })

export class NavbarService {

    public allModules: any[] = [];
    public AllPlaces: any[] = [];

    constructor(private http: HttpClient, private allToasts: Alltoasts) {

    }

    getCityInfo(): Observable<any> {
        console.log('NavbarService.getCityInfo() called');
        const cityId = localStorage.getItem('city');
        console.log('City ID from localStorage:', cityId);

        if (cityId != null) {
            console.log('Making API call to get city info');
            return this.http.get<any>(AppApiUrls.CityInfo(), {
                params: {
                    city_id: cityId,
                    lang: localStorage.getItem('lang') || 'ua',
                }
            });
        } else {
            console.log('No city ID found, returning empty observable');
            // Return an empty observable that completes immediately
            return new Observable(observer => {
                observer.next({ data: null });
                observer.complete();
            });
        }
    }

    fetchModels(): Observable<any> {
        console.log('NavbarService.fetchModels() called');
        const prof = localStorage.getItem('profs');
        const city = localStorage.getItem('city');
        console.log('Fetching modules with params:', { prof, city });

        return this.http.get<any>(AppApiUrls.GetAllModules(),
            {
                params: {
                    prof: prof,
                    city: city
                }
            });
    }

    getAllPlaces = () => {
        return this.http.get<any>(AppApiUrls.GetAllPlaces)
            .subscribe(
                (val) => {
                    this.AllPlaces = val as any;
                },
                response => {
                    console.log('POST call in error', response);
                    this.allToasts.showError();
                },
                () => {

                });
    } // TODO убрать получение отделения оно есть в сити сервисе

    changePlace = place => {
        const id = localStorage.getItem('id');
        const body = new HttpParams()
            .set('place', place);
        return this.http.put<any>(AppApiUrls.getAllUsers() + id,
            body)
            .subscribe(
                (val) => {
                    localStorage.setItem('place', place);
                    localStorage.setItem('city', val.city_id);
                    localStorage.setItem('type', val.check_type);
                    localStorage.setItem('city-name', val.cities.description.name);

                },
                response => {
                    console.log('POST call in error', response);
                    this.allToasts.showError();
                },
                () => {
                    window.location.reload();
                });
    }

    getNews(): Observable<NewsData> {
        return this.http.get<NewsData>(AppApiUrls.news(), {
            params: {
                user_id: localStorage.getItem('id')
            }
        });
    }

    getNewsByID(id: number): Observable<NewsDataInfo> {
        return this.http.get<NewsDataInfo>(AppApiUrls.news() + '/' + id);
    }

    checkedNews(body: NewsData): Observable<NewsData> {
        return this.http.post<NewsData>(AppApiUrls.newsConfirmation(), body);
    }

}

