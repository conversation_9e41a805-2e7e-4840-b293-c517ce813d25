import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { NavbarService } from '../navbar.service';

@Injectable()
export class ModulesResolver implements Resolve<any> {
    constructor(private modulesService: NavbarService) {
    }
    resolve(route: ActivatedRouteSnapshot): Observable<any> {
        return this.modulesService.fetchModels().pipe(
            catchError(error => {
                console.error('Error loading modules:', error);
                return of([]); // Return empty array on error
            })
        );
    }
}
