import {
    Component,
    OnInit,
    TemplateRef
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SpeedTestService } from 'ng-speed-test';
import { AuthService } from '../@shared/login-page/auth.service';
import { NavbarService } from '../@shared/navbar/navbar.service';

import { FlatTreeControl } from '@angular/cdk/tree';
import { MatBottomSheet } from '@angular/material/bottom-sheet';
import { MatTreeFlatDataSource, MatTreeFlattener } from '@angular/material/tree';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { takeUntil } from 'rxjs/operators';
import { environment } from "../../environments/environment";
import { AuthStateService } from '../@shared/services/auth-state.service';
import { Unsubscribe } from '../@shared/unsubscribe';
import { DevToolsComponent } from '../dev-tools/dev-tools.component';
import { News, NewsItem } from '../notification/interface/notification.interface';

interface FoodNode {
    id: number;
    img: string;
    parent_id: number;
    type: number;
    url: string;
    url_ang: string;
    name: string;
    children?: FoodNode[];
}

interface ExampleFlatNode {
    children: boolean;
    expandable: boolean;
    name: string;
    level: number;
    url_ang: string;
}

@Component({
    selector: 'app-navbar',
    templateUrl: './navbar.component.html',
    styleUrls: ['./navbar.component.scss']
})

export class NavbarComponent extends Unsubscribe implements OnInit {
    public isCollapsed = true;
    public UserName: string;
    public UserImg: string;
    public UserProfs: string;
    public UserPlace: string;
    userCity: string;
    treeControl;
    treeFlattener;
    dataSource;
    // searchControl = new FormControl();
    originalTreeData: any[];
    showNotifications = false;
    news: NewsItem;
    newsInfo: any;
    isUnreadFilterActive = false;
    disableDropdown = false;
    transformer = (node: FoodNode, level: number) => {
        return {
            expandable: !!node.children && node.children.length > 0,
            name: node.name,
            level,
            id: node.id,
            img: node.img,
            parent_id: node.parent_id,
            type: node.type,
            url: node.url,
            url_ang: node?.url_ang,
        };
    }
    searchQuery: string = '';
    filteredTreeData: any[] = [];


    constructor(public translate: TranslateService,
        public Auth: AuthService,
        public NavbarService: NavbarService,
        private speedTestService: SpeedTestService,
        private _bottomSheet: MatBottomSheet,
        private modalService: NgbModal,
        private route: ActivatedRoute,
        private router: Router,
        private authStateService: AuthStateService
    ) {
        super();
        translate.addLangs(['ru', 'ua', 'en']);
        translate.setDefaultLang('ua');
        translate.use(localStorage.getItem('lang') || 'ua');
        this.treeControl = new FlatTreeControl<ExampleFlatNode>(
            node => node.level, node => node.expandable);
        this.treeFlattener = new MatTreeFlattener(
            this.transformer, node => node.level, node => node.expandable, node => node.children);
        this.dataSource = new MatTreeFlatDataSource(this.treeControl, this.treeFlattener);

        setTimeout(() => {
            this.speedTest();
        }, 15000);
    }

    ngOnInit(): void {
        console.log('NavbarComponent.ngOnInit() called');
        console.log('Is authenticated:', this.Auth.isAuthenticated());
        console.log('LocalStorage contents:', {
            id: localStorage.getItem('id'),
            name: localStorage.getItem('name'),
            city: localStorage.getItem('city'),
            profs: localStorage.getItem('profs')
        });

        this.updateUserInfo();

        // Only load authenticated data if user is logged in
        if (this.Auth.isAuthenticated()) {
            this.get(); // Load modules
            this.getCityInfo();
            this.getNews();
        }

        this.NavbarService.getAllPlaces();
        this.checkPathname();
        this.onChange();
        this.subscribeToAuthStateChanges();
    }

    subscribeToAuthStateChanges(): void {
        console.log('NavbarComponent.subscribeToAuthStateChanges() called');
        this.authStateService.authStateChanged$
            .pipe(takeUntil(this.$destroy))
            .subscribe(isAuthenticated => {
                console.log('Auth state changed:', isAuthenticated);
                if (isAuthenticated) {
                    console.log('Calling refreshAfterLogin()');
                    this.refreshAfterLogin();
                }
            });
    }

    updateUserInfo(): void {
        console.log('NavbarComponent.updateUserInfo() called');
        this.UserName = localStorage.getItem('name');
        this.UserImg = localStorage.getItem('image');
        this.UserProfs = localStorage.getItem('profs');
        this.UserPlace = localStorage.getItem('place');
        this.userCity = localStorage.getItem('city-name');
        console.log('Updated user info:', {
            name: this.UserName,
            profs: this.UserProfs,
            city: this.userCity
        });
    }

    refreshAfterLogin(): void {
        console.log('NavbarComponent.refreshAfterLogin() called');
        this.updateUserInfo();
        this.get();
        this.getCityInfo();
        this.getNews();
    }

    onChange(): void {
        this.router.events.pipe(takeUntil(this.$destroy))
            .subscribe(event => {
                if (event instanceof NavigationEnd) {
                    console.log('Navigation ended to:', event.url);
                    this.checkPathname();
                    // Refresh modules and user info when navigating to authenticated routes
                    if (event.url !== '/login' && this.Auth.isAuthenticated()) {
                        console.log('Refreshing data after navigation to authenticated route');
                        this.updateUserInfo();
                        this.get(); // Refresh modules
                        this.getNews(); // Refresh news
                    }
                }
            });
    }


    checkPathname(): void {
        this.disableDropdown = window.location.pathname !== '/';
    }

    getCityInfo(): void {
        console.log('NavbarComponent.getCityInfo() called');
        this.NavbarService.getCityInfo().subscribe({
            next: value => {
                console.log('City info received:', value);
                if (value && value.data) {
                    localStorage.setItem('cityInfo', JSON.stringify(value.data));
                    console.log('City info saved to localStorage');
                } else {
                    console.log('No city info data received');
                }
            },
            error: error => {
                console.error('Error getting city info:', error);
            }
        });
    }

    changeLang(langSelect, lang): void {
        localStorage.setItem('lang', lang);
        this.translate.use(langSelect.value);
        window.location.reload();
    }

    get(): void {
        console.log('NavbarComponent.get() called');
        console.log('Is authenticated:', this.Auth.isAuthenticated());

        if (this.Auth.isAuthenticated()) {
            console.log('Fetching modules...');
            this.NavbarService.fetchModels()
                .subscribe({
                    next: value => {
                        console.log('Modules received:', value);
                        this.setModulesData(value);
                    },
                    error: error => {
                        console.error('Error loading modules:', error);
                        // Set empty data on error to prevent UI issues
                        this.setModulesData([]);
                    }
                });
        } else {
            console.log('Not authenticated, skipping module fetch');
        }
    }

    private setModulesData(value: any): void {
        console.log('NavbarComponent.setModulesData() called with:', value);
        const modules = value || [];
        console.log('Setting modules data, count:', modules.length);
        this.dataSource.data = modules;
        this.originalTreeData = [...modules];
        this.filteredTreeData = [...modules];
    }

    hasChild = (_: number, node: ExampleFlatNode) => node.expandable;


    speedTest = () => {
        this.speedTestService.getMbps(
            {
                iterations: 10,
                file: {
                    path: 'background.jpg',
                    size: 147800
                } as any,
                retryDelay: 1500,
            }
        ).subscribe(
            (speed) => {
                setTimeout(() => {
                    this.speedTest();
                }, 3e6);
                console.log('Your speed is ' + speed);
            }
        );
    }

    openBottomSheet(): void {
        this._bottomSheet.open(DevToolsComponent);
    }

    toggleNotifications(): void {
        this.showNotifications = !this.showNotifications;
        this.isUnreadFilterActive = false;
    }

    getNews = (): void => {
        console.log('NavbarComponent.getNews() called');
        console.log('Is authenticated:', this.Auth.isAuthenticated());
        console.log('User ID:', localStorage.getItem('id'));

        if (!this.Auth.isAuthenticated() || !localStorage.getItem('id')) {
            console.log('Not authenticated or no user ID, skipping news fetch');
            return;
        }

        this.NavbarService.getNews()
            .pipe(takeUntil(this.$destroy))
            .subscribe({
                next: value => {
                    console.log('News received:', value);
                    this.news = value.data;
                },
                error: error => {
                    console.error('Error getting news:', error);
                }
            });
    }

    getNewsById(id: number): void {
        this.NavbarService
            .getNewsByID(id)
            .pipe(takeUntil(this.$destroy))
            .subscribe({
                next: (value) => {
                    this.newsInfo = value.data;
                }
            });
    }

    create(id: number, modal: TemplateRef<News>): void {
        this.modalService.open(modal, { size: 'lg' });
        this.getNewsById(id);
    }

    close(): void {
        this.modalService.dismissAll();
    }

    openAttachment(url: string): void {
        const fullUrl = environment.url + url;
        window.open(fullUrl, '_blank');
    }

    getFileName(url: string): string {
        const parts = url.split('/');
        return parts[parts.length - 1];
    }

    checkedNews(id: number, modal: TemplateRef<News>, confirmation: number): void {
        const actionOpened = {
            user_id: localStorage.getItem('id'),
            news_id: id,
            action: 'opened'
        };

        if (confirmation === 0) {
            this.create(id, modal);
            this.sendNewsAction(actionOpened);
        } else if (confirmation === 1) {
            this.create(id, modal);
        }
    }

    sendNewsAction(body: any): void {
        this.NavbarService.checkedNews(body)
            .pipe(takeUntil(this.$destroy))
            .subscribe({
                complete: () => {
                    this.getNews();
                }
            });
    }

    actionConfirmed(id: number): void {
        const actionConfirmed = {
            user_id: localStorage.getItem('id'),
            news_id: id,
            action: 'confirmed'
        };
        this.sendNewsAction(actionConfirmed);
    }

    toggleUnreadFilter(event?: Event): void {
        event.stopPropagation();
        this.isUnreadFilterActive = !this.isUnreadFilterActive;

    }

    showNoUnreadNotifications(): boolean {
        if (this.news.unread === 0) {
            return this.isUnreadFilterActive ? this.news?.news.every(item => item.confirmation.length === 0) : true;
        }
    }

    replaceN(): string {
        return this.newsInfo?.description.replaceAll('\n', '<br>');
    }

    filterTree(query: string): void {
        if (!query) {
            this.filteredTreeData = this.deepCopy(this.originalTreeData);
        } else {
            const copy = this.deepCopy(this.originalTreeData);
            this.filteredTreeData = copy.filter(node => this.filterNode(node, query.toLowerCase()));
        }
        this.dataSource.data = this.filteredTreeData;
    }

    filterNode(node: any, query: string): boolean {
        if (node.name.toLowerCase().includes(query) && !node.children) {
            return true;
        }
        if (node.children) {
            node.children = node.children.filter((child: any) => this.filterNode(child, query));
            return node.children.length > 0;
        }
        return false;
    }

    // Вспомогательный метод для создания глубокой копии данных
    deepCopy(data: any) {
        return JSON.parse(JSON.stringify(data));
    }

    clearSearch(): void {
        this.searchQuery = '';
        this.filterTree('');
    }

}
